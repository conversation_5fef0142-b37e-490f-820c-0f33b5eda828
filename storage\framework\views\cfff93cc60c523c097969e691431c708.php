<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Portal PWB - Superadmin Dashboard</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Portal PWB" name="description" />
    <meta content="PWB" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/logo-small.png')); ?>">
    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')([
    'resources/assets/css/bootstrap.min.css',
    'resources/assets/css/icons.min.css',
    'resources/assets/css/app.min.css',
    'resources/css/app.css',
    'resources/css/superadmin-dashboard.css',
    'resources/css/attachment-display.css',
    'resources/css/superadmin-scaling.css'
    ]); ?>
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/metisMenu/3.0.7/metisMenu.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Vite JS Resources -->
    <?php echo app('Illuminate\Foundation\Vite')([
    'resources/js/superadmin-dashboard-sites-data.js',
    'resources/js/superadmin-dashboard-chart.js',
    'resources/js/superadmin-dashboard-details.js',
    'resources/js/superadmin-dashboard-scroller.js',
    'resources/js/superadmin-dashboard-units-modal.js',
    'resources/js/superadmin-dashboard-best-parts.js',
    'resources/js/superadmin-dashboard-part-type-modal.js',
    'resources/js/superadmin-dashboard-jasa-karyawan-modal.js',
    'resources/js/superadmin-scaling.js',
    'resources/js/superadmin-mobile-menu.js'
    ]); ?>
    <style>
        :root {
            --primary-color: #2a69a8;
            --secondary-color: rgba(42, 105, 168, 0.5);
            --accent-color: rgb(40, 21, 211);
            --accent-hover-color: rgb(60, 41, 231);
            --highlight-color: rgb(251, 255, 0);
            --danger-color: #ff5d48;
            --success-color: #1bb99a;
            --info-color: #3db9dc;
            --warning-color: #f1734f;
            --text-color: #343a40;
            --text-muted: #6c757d;
            --border-color: rgba(0, 0, 0, 0.1);
            --card-bg-color: rgba(255, 255, 255, 0.95);
        }

        .btn:hover {
            border-radius: 50px;
            background: #e0e0e0;
            box-shadow: inset 20px 20px 60px var(--primary-color),
                inset -20px -20px 60px #ffffff;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            background: url('<?php echo e(asset('assets/images/homewalpaper.jpg')); ?>');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            font-family: 'Segoe UI', sans-serif;
            position: relative;
            overflow-x: hidden;
        }

        .dashboard-container {
            width: 100%;
            max-width: 1600px;
            padding: 0 15px;
            margin: 0 auto;
        }

        .login-theme-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
            margin-bottom: 20px;
            border-radius: 0 0 10px 10px;
        }

        .header-top {
            display: none;
            /* Hide on desktop, only show on mobile */
        }

        .company-logo {
            display: flex;
            align-items: center;
        }

        .company-logo img {
            height: 40px;
            margin-right: 10px;
        }

        .company-name {
            font-size: 1.2rem;
            font-weight: 700;
            margin: 0;
            color: var(--primary-color);
        }

        /* Mobile Menu Toggle Button */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background-color: rgba(42, 105, 168, 0.1);
        }

        /* Mobile Menu Close Button */
        .mobile-menu-close {
            display: none;
            background: none;
            border: none;
            color: var(--danger-color);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .mobile-menu-close:hover {
            background-color: rgba(42, 105, 168, 0.1);
        }

        /* Mobile Menu Overlay */
        .mobile-menu-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 998;
        }

        /* Mobile Menu Header */
        .mobile-menu-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            padding-top: 30px;
            /* Add padding to top to avoid overlap with close button */
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .menu-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
            padding: 0;
        }

        /* Header Right */
        .header-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        /* Month Picker Container */
        .month-picker-container {
            margin-right: 15px;
        }

        /* Navigation Links */
        .nav-links {
            display: flex;
            flex-wrap: wrap;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            margin: 0 5px 5px 0;
            border-radius: 8px;
            text-decoration: none;
            color: var(--primary-color);
            background-color: rgba(42, 105, 168, 0.1);
            transition: all 0.3s ease;
            border: 1px solid var(--primary-color);
        }

        .nav-link i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .nav-link:hover {
            background-color: var(--primary-color);
            color: #fff;
        }

        .nav-link.active {
            background-color: var(--primary-color);
            color: #fff;
        }

        .nav-link-danger {
            color: var(--danger-color) !important;
            background-color: rgba(255, 93, 72, 0.1) !important;
            border: 1px solid var(--danger-color) !important;
        }

        .nav-link-danger:hover {
            background-color: var(--danger-color) !important;
            color: #fff !important;
        }

        .card {
            background-color: var(--card-bg-color) !important;
            border-radius: 10px !important;
            border: 1px solid var(--border-color) !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 1rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: rgba(42, 105, 168, 0.05) !important;
            border-bottom: 1px solid var(--border-color) !important;
            padding: 0.75rem 1rem;
        }

        .card-title {
            margin: 0;
            color: var(--text-color) !important;
            font-weight: 600;
            font-size: 1rem;
        }

        .card-body {
            padding: 1rem;
        }

        .total-summary-card {
            background: linear-gradient(135deg, rgba(42, 105, 168, 0.9) 0%, rgba(29, 80, 133, 0.8) 100%) !important;
            color: white;
        }

        .stat-card {
            border-radius: 10px;
            padding: 1.5rem;
            height: 100%;
            position: relative;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid var(--border-color);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
        }

        .stat-card-cost::before {
            background: var(--accent-color);
        }

        .stat-card-margin::before {
            background: var(--info-color);
        }

        .stat-card-profit::before {
            background: var(--success-color);
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }

        .stat-label {
            font-size: 1rem;
            color: var(--text-color);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.2;
            position: absolute;
            top: 1rem;
            right: 1rem;
            color: white;
        }

        .cost-color {
            color: var(--accent-color);
        }

        .margin-color {
            color: var(--info-color);
        }

        .profit-color {
            color: var(--success-color);
        }
        .utang-color {
            color: var(--secondary-color);
        }

        .part-type-margin {
            margin-top: 1rem;
        }

        .part-type-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .part-type-item:last-child {
            border-bottom: none;
        }

        .part-type-name {
            display: flex;
            align-items: center;
        }

        .part-type-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            color: white;
        }

        .ac-bg {
            background-color: var(--success-color);
        }

        .tyre-bg {
            background-color: var(--info-color);
        }

        .fabrikasi-bg {
            background-color: var(--accent-color);
        }

        .month-picker-container {
            display: flex;
            align-items: center;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-color);
            font-size: 1em;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
        }

        .form-control:focus {
            outline: none;
            background: #ffffff;
            color: var(--text-color);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(42, 105, 168, 0.25);
        }

        .btn {
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary {
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            background: rgba(42, 105, 168, 0.1);
        }

        .btn-outline-primary:hover {
            border-radius: 50px;
            box-shadow: inset 20px 20px 60pxrgb(252, 251, 251),
                inset -20px -20px 60px #ffffff;
            background: var(--primary-color);
            color: #ffffff;
            border-color: var(--primary-color);
        }

        .btn-primary {
            border-radius: 50px;
            box-shadow: inset 20px 20px 60px #bebebe,
                inset -20px -20px 60px #ffffff;
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: #1d5085;
            border-color: #1d5085;
            color: #ffffff;
        }

        .btn-danger {
            background: var(--danger-color);
            border-color: var(--danger-color);
            color: #ffffff;
        }

        .btn-danger:hover {
            background: #e04b3a;
            border-color: #e04b3a;
            color: #ffffff;
        }

        .btn-logout {
            background: rgba(255, 93, 72, 0.1);
            color: var(--danger-color);
            border: 1px solid var(--danger-color);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .btn-logout:hover {
            background-color: var(--danger-color);
            color: #ffffff;
        }

        .btn-logout i {
            margin-right: 0.5rem;
        }

        .content-wrapper {
            padding: 10px 5px;
            max-width: 100%;
            margin: 0 auto;
        }

        .progress {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-bar {
            border-radius: 10px;
        }

        .badge {
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            font-weight: 500;
        }

        .text-muted {
            color: var(--text-muted) !important;
        }

        h6,
        .h6,
        h5,
        .h5,
        h4,
        .h4,
        h3,
        .h3 {
            color: var(--text-color);
        }

        small {
            color: var(--text-muted);
        }

        /* Removed unused CSS for Target Site section */

        .revenue-card {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            transition: all 0.3s ease;
            padding: 15px;
            margin-bottom: 10px;
        }

        .revenue-card:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .status-card {
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 12px;
        }

        .status-card h5,
        .status-card h6,
        .status-card .h5 {
            color: var(--text-color) !important;
        }

        .header-right {
            display: flex;
            align-items: center;
        }



        @media (max-width: 992px) {
            .login-theme-header {
                padding: 0;
                flex-direction: column;
                align-items: stretch;
            }

            /* Hide desktop logo on mobile */
            .login-theme-header>.company-logo {
                display: none;
            }

            /* Show header-top on mobile */
            .header-top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                padding: 10px 15px;
            }

            /* Show mobile menu toggle button */
            .mobile-menu-toggle {
                display: block;
            }

            /* Mobile menu styling */
            .header-right {
                position: fixed;
                top: 0;
                /* Position at the top */
                right: -100%;
                /* Hide off-screen initially using percentage */
                width: 85%;
                /* Use percentage width to ensure it fits on all screens */
                max-width: 300px;
                /* Set a maximum width */
                height: auto;
                /* Auto height instead of 100% */
                max-height: 80vh;
                /* Maximum height of 80% of viewport height */
                background-color: #fff;
                box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
                padding: 15px;
                z-index: 999;
                overflow-y: auto;
                transition: right 0.3s ease;
                flex-direction: column;
                border-radius: 0 0 0 10px;
                /* Rounded corners on bottom left */
            }

            /* Show mobile menu close button */
            .mobile-menu-close {
                display: block;
            }

            /* When mobile menu is active */
            .header-right.active {
                right: 0;
            }

            /* Show overlay when mobile menu is active */
            .mobile-menu-overlay.active {
                display: block;
            }

            /* Navigation links in mobile view */
            .nav-links {
                flex-direction: column;
                width: 100%;
                margin-top: 20px;
            }

            .nav-link {
                width: 100%;
                margin: 0 0 10px 0;
                padding: 12px 15px;
                justify-content: flex-start;
            }

            .nav-link i {
                width: 24px;
                text-align: center;
                margin-right: 10px;
            }

            .month-picker-container {
                width: 100%;
                margin-bottom: 15px;
            }

            .month-picker-container form {
                width: 100%;
            }

            .month-picker-container .d-flex {
                width: 100%;
                justify-content: space-between;
                flex-wrap: nowrap;
            }

            .month-picker-container .form-control {
                font-size: 0.9rem;
                padding: 0.375rem 0.5rem;
                height: auto;
            }

            .month-picker-container .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.8rem;
            }
        }

        /* Mobile specific styles */
        @media (max-width: 576px) {
            .login-theme-header {
                padding: 8px 12px;
            }

            .company-logo {
                flex-direction: row;
                align-items: center;
            }

            .company-logo img {
                height: 28px;
                margin-right: 6px;
                margin-bottom: 0;
            }

            .company-name {
                font-size: 0.8rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 160px;
            }

            .mobile-menu-toggle {
                padding: 5px;
                font-size: 1.3rem;
            }

            .mobile-menu-close {
                padding: 5px;
                font-size: 1.3rem;
                top: 5px;
                right: 5px;
            }

            .header-right {
                width: 85%;
                max-width: 280px;
                padding: 10px;
            }

            .nav-links {
                margin-top: 15px;
            }

            .nav-link {
                padding: 8px 10px;
                font-size: 0.85rem;
                margin-bottom: 8px;
            }

            .nav-link i {
                font-size: 1rem;
                margin-right: 6px;
                width: 20px;
            }

            /* Adjust month picker for very small screens */
            .month-picker-container .d-flex {
                flex-wrap: nowrap;
            }

            .month-picker-container .form-control {
                font-size: 0.8rem;
                padding: 0.25rem;
            }

            .month-picker-container .btn {
                padding: 0.2rem 0.4rem;
                font-size: 0.75rem;
            }
        }
    </style>
</head>

<body>
    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Login Theme Header -->
        <header class="login-theme-header">
            <!-- Company Logo (Visible on all devices) -->
            <div class="company-logo">
                <img src="<?php echo e(asset('assets/images/logo-small.png')); ?>" alt="PWB Logo">
                <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
            </div>

            <!-- Mobile Header Top (Only visible on mobile) -->
            <div class="header-top">
                <div class="company-logo">
                    <img src="<?php echo e(asset('assets/images/logo-small.png')); ?>" alt="PWB Logo">
                    <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
                </div>

                <!-- Mobile Menu Toggle Button -->
                <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="mdi mdi-menu"></i>
                </button>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

            <!-- Header Right (Navigation) -->
            <div class="header-right" id="mobileMenu">
                <!-- Close Button for Mobile -->
                <button type="button" class="mobile-menu-close d-lg-none" id="mobileMenuClose">
                    <i class="mdi mdi-close"></i>
                </button>

                <div class="mobile-menu-header d-lg-none">
                    <h5 class="menu-title">Menu Navigasi</h5>
                </div>

                <div class="month-picker-container">
                    <form action="<?php echo e(route('superadmin.dashboard')); ?>" method="GET" id="dateRangeForm">
                        <div class="d-flex align-items-center flex-wrap">
                            <!-- Simple Date Range Picker -->
                            <div class="d-flex">
                                <input type="date" id="start-date" name="start_date" class="form-control form-control-sm me-1"
                                    value="<?php echo e($startDate ? $startDate->format('Y-m-d') : ''); ?>" placeholder="Tanggal Mulai">
                                <span class="align-self-center mx-1">-</span>
                                <input type="date" id="end-date" name="end_date" class="form-control form-control-sm ms-1"
                                    value="<?php echo e($endDate ? $endDate->format('Y-m-d') : ''); ?>" placeholder="Tanggal Akhir">
                            </div>

                            <!-- Apply Button -->
                            <button type="submit" class="btn btn-sm btn-primary ms-2">
                                <i class="mdi mdi-filter"></i> Terapkan
                            </button>
                        </div>
                    </form>
                </div>

                <div class="nav-links">
                    <a href="<?php echo e(route('superadmin.dashboard')); ?>" class="nav-link <?php echo e(request()->routeIs('superadmin.dashboard') ? 'active' : ''); ?>">
                        <i class="mdi mdi-view-dashboard"></i> <span>Dashboard</span>
                    </a>
                    <a href="<?php echo e(route('superadmin.invoices')); ?>" class="nav-link <?php echo e(request()->routeIs('superadmin.invoices') ? 'active' : ''); ?>">
                        <i class="mdi mdi-file-document-outline"></i> <span>Invoice</span>
                    </a>
                    <a href="<?php echo e(route('superadmin.parts')); ?>" class="nav-link <?php echo e(request()->routeIs('superadmin.parts') ? 'active' : ''); ?>">
                        <i class="mdi mdi-package-variant"></i> <span>Part</span>
                    </a>
                    <a href="<?php echo e(route('superadmin.price-list')); ?>" class="nav-link <?php echo e(request()->routeIs('superadmin.price-list') ? 'active' : ''); ?>">
                        <i class="mdi mdi-tag-multiple"></i> <span>Price List</span>
                    </a>
                    <a href="<?php echo e(route('logout')); ?>" class="nav-link nav-link-danger">
                        <i class="mdi mdi-logout"></i> <span>Logout</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Skeleton Loader -->
        <div id="skeleton-loader" class="skeleton-container" style="display: block; padding-top: 100px;">
            <div class="container-fluid py-4">
                <!-- Financial Summary Cards Skeleton -->
                <div class="row mb-0">
                    <div class="col-md-4 mb-2">
                        <div class="skeleton-card">
                            <div class="skeleton-body">
                                <div class="skeleton-line skeleton-title"></div>
                                <div class="skeleton-line" style="height: 30px; margin-top: 15px;"></div>
                                <div class="skeleton-line"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-2">
                        <div class="skeleton-card">
                            <div class="skeleton-body">
                                <div class="skeleton-line skeleton-title"></div>
                                <div class="skeleton-line" style="height: 30px; margin-top: 15px;"></div>
                                <div class="skeleton-line"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-2">
                        <div class="skeleton-card">
                            <div class="skeleton-body">
                                <div class="skeleton-line skeleton-title"></div>
                                <div class="skeleton-line" style="height: 30px; margin-top: 15px;"></div>
                                <div class="skeleton-line"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Summary Section Skeleton -->
                <div class="row mb-3">
                    <div class="col">
                        <div class="skeleton-card">
                            <div class="skeleton-body">
                                <div class="skeleton-line skeleton-title"></div>
                                <div class="skeleton-line" style="height: 40px; margin-top: 15px;"></div>
                                <div class="skeleton-line"></div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Monthly Invoice Chart Skeleton -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="skeleton-card">
                            <div class="skeleton-header"></div>
                            <div class="skeleton-body">
                                <div class="skeleton-chart" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sites Grid Skeleton -->
                <div class="row g-3">
                    <div class="col-xl-4 col-md-6">
                        <div class="skeleton-card">
                            <div class="skeleton-header"></div>
                            <div class="skeleton-body">
                                <div class="skeleton-line"></div>
                                <div class="skeleton-progress"></div>
                                <div class="skeleton-line"></div>
                                <div class="skeleton-line"></div>
                                <div class="skeleton-indicators"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-4 col-md-6">
                        <div class="skeleton-card">
                            <div class="skeleton-header"></div>
                            <div class="skeleton-body">
                                <div class="skeleton-line"></div>
                                <div class="skeleton-progress"></div>
                                <div class="skeleton-line"></div>
                                <div class="skeleton-line"></div>
                                <div class="skeleton-indicators"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-4 col-md-6">
                        <div class="skeleton-card">
                            <div class="skeleton-header"></div>
                            <div class="skeleton-body">
                                <div class="skeleton-line"></div>
                                <div class="skeleton-progress"></div>
                                <div class="skeleton-line"></div>
                                <div class="skeleton-line"></div>
                                <div class="skeleton-indicators"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content-wrapper" style="display: none;">


            <div class="">
                <div class="card-body p-0">
                    <!-- Summary Section -->
                    <div class="row mb-0">
                        <div class="col">
                            <div class="card total-summary-card">
                                <div class="card-body py-2">
                                    <div class="row">
                                        <div class="col">
                                            <div class="total-invoice-info pl-4">
                                                <!-- <h5 class="mb-1 font-weight-bold text-white h2 text-uppercase">Net Profit <?php echo e($monthName); ?></h5> -->
                                                <!-- <h5 class="mb-1 font-weight-bold text-white h2 text-uppercase">LABA BERSIH <?php echo e($dateRangeText ?? $monthName); ?></h5> -->
                                                <h5 class="mb-1 font-weight-bold text-white h2 text-uppercase">LABA <?php echo e($dateRangeText ?? $monthName); ?></h5>
                                                <h2 class="mb-0 display-3 fw-bold font-weight-bold text-white">Rp <?php echo e(number_format($totalInvoiceAmount ?? 0, 0, ',', '.')); ?></h2>
                                                <div class="mt-1 mb-4">
                                                    <?php if(isset($invoiceDifference) && $invoiceDifference > 0): ?>
                                                    <span class="badge bg-success">
                                                        <i class="mdi mdi-arrow-up-bold"></i>
                                                        +<?php echo e(number_format($invoiceDifferencePercent ?? 0, 1)); ?>%
                                                        (Rp <?php echo e(number_format($invoiceDifference, 0, ',', '.')); ?>)
                                                    </span>
                                                    <?php elseif(isset($invoiceDifference) && $invoiceDifference < 0): ?>
                                                    <span class="badge bg-danger">
                                                        <i class="mdi mdi-arrow-down-bold"></i>
                                                        <?php echo e(number_format($invoiceDifferencePercent ?? 0, 1)); ?>%
                                                        (Rp <?php echo e(number_format(abs($invoiceDifference), 0, ',', '.')); ?>)
                                                    </span>
                                                    <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="mdi mdi-minus"></i>
                                                        0% (Tidak ada perubahan)
                                                    </span>
                                                    <?php endif; ?>
                                                    <span class="opacity-75 ms-1 mb-4 pb-4">dari bulan lalu</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-auto ms-auto">
                                            <div class="filter-container p-2 d-flex align-items-center justify-content-end">
                                                <form action="<?php echo e(route('superadmin.dashboard')); ?>" method="GET" id="mainFilterForm">
                                                    <!-- No hidden date inputs - we'll use the values from the date range form directly -->
                                                    <div class="d-flex flex-wrap align-items-center">
                                                        <div class="me-2 mb-2">
                                                            <select id="division-filter" name="division" class="btn btn-sm btn-outline-light" style="width: auto;">
                                                                <option value="" style="color: #333;">Semua Divisi</option>
                                                                <option value="ac" style="color: #333;">AC</option>
                                                                <option value="fabrikasi" style="color: #333;">FABRIKASI</option>
                                                                <option value="tyre" style="color: #333;">TYRE</option>
                                                            </select>
                                                        </div>
                                                        <div class="me-2 mb-2">
                                                            <select id="site-filter" name="site" class="btn btn-sm btn-outline-light" style="width: auto;">
                                                                <option value="" style="color: #333;">Semua Site</option>
                                                                <?php $__currentLoopData = \App\Models\Site::where('site_id', '!=', 'WHO')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option value="<?php echo e($site->site_id); ?>" <?php echo e(request('site') == $site->site_id ? 'selected' : ''); ?> style="color: #333;"><?php echo e($site->site_name); ?></option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                        </div>
                                                        <div class="mb-2">
                                                            <button type="submit" class="btn btn-sm btn-light me-1" id="searchBtn" style="width: auto;">
                                                                <i class="mdi mdi-magnify"></i> Cari
                                                            </button>
                                                            <a href="<?php echo e(route('superadmin.dashboard')); ?>" class="btn btn-sm btn-outline-light" style="width: auto;">
                                                                <i class="mdi mdi-refresh"></i> Reset
                                                            </a>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row m-0">
                        <div class="col-md-3 mb-2">
                            <div class="position-relative p-0 m-0">
                                <div class="neumorphism stat-card  total-summary-card text-white">
                                    <i class="mdi mdi-currency-usd stat-icon"></i>
                                    <h5 class="stat-label text-white">Cost (Biaya)</h5>
                                    <!-- <h2 class="stat-value text-white">Rp <?php echo e(number_format(isset($costData) ? $costData : 0, 0, ',', '.')); ?></h2> -->
                                    <h4 class="h4 text-white"><i>Data Pembelian belum tersedia</i></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-2">
                            <div class="card-body position-relative p-0 m-0">
                                <div class="neumorphism stat-card total-summary-card text-white">
                                    <i class="mdi mdi-chart-line stat-icon margin-color"></i>
                                    <h5 class="stat-label text-white">Margin Keuntungan</h5>
                                    <h4 class="h4 text-white"><i>Data Keunganan belum tersedia</i></h4>
                                    <!-- <h2 class="stat-value margin-color  text-white"><?php echo e(number_format($profitMarginPercentage ?? 0, 1)); ?>%</h2> -->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-2">
                            <div class="card-body position-relative p-0 m-0">
                                <div class="neumorphism stat-card total-summary-card">
                                    <i class="mdi mdi-cash-multiple stat-icon profit-color"></i>
                                    <h5 class="stat-label text-white">Piutang</h5>
                                    <h2 class="stat-value profit-color text-white">Rp <?php echo e(number_format($accountsReceivableData ?? 0, 0, ',', '.')); ?></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-2">
                            <div class="card-body position-relative p-0 m-0">
                                <div class="neumorphism stat-card total-summary-card">
                                    <i class="mdi mdi-cash stat-icon profit-color"></i>
                                    <h5 class="stat-label text-white">Utang</h5>
                                    <h4 class="h4 utang-color  text-white"><i>Data Utang belum tersedia</i></h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Simple Loader Script -->
                    <script>
                        // Show skeleton loader when page starts loading
                        window.addEventListener('load', function() {
                            const skeletonLoader = document.getElementById('skeleton-loader');
                            const contentWrapper = document.querySelector('.content-wrapper');

                            // Hide skeleton loader and show content when page is fully loaded
                            skeletonLoader.style.display = 'none';
                            contentWrapper.style.display = 'block';
                        });

                        document.addEventListener('DOMContentLoaded', function() {
                            const startDateInput = document.getElementById('start-date');
                            const endDateInput = document.getElementById('end-date');
                            const dateRangeForm = document.getElementById('dateRangeForm');
                            const contentWrapper = document.querySelector('.content-wrapper');
                            const skeletonLoader = document.getElementById('skeleton-loader');

                            // Set default date range if not already set
                            if (!startDateInput.value || !endDateInput.value) {
                                const today = new Date();
                                const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

                                // Format dates as YYYY-MM-DD
                                startDateInput.value = formatDate(firstDayOfMonth);
                                endDateInput.value = formatDate(today);
                            }

                            // Helper function to format date as YYYY-MM-DD
                            function formatDate(date) {
                                const year = date.getFullYear();
                                const month = String(date.getMonth() + 1).padStart(2, '0');
                                const day = String(date.getDate()).padStart(2, '0');
                                return `${year}-${month}-${day}`;
                            }

                            // Initialize filter form
                            const mainFilterForm = document.getElementById('mainFilterForm');
                            const divisionFilter = document.getElementById('division-filter');
                            const siteFilter = document.getElementById('site-filter');

                            // Add event listener to search button
                            document.getElementById('searchBtn').addEventListener('click', function(e) {
                                e.preventDefault();

                                // Get the date values from the date range form
                                const startDateValue = startDateInput.value;
                                const endDateValue = endDateInput.value;

                                // Create hidden inputs for the dates in the main filter form
                                const mainForm = document.getElementById('mainFilterForm');

                                // Remove any existing date inputs first
                                const existingStartDate = mainForm.querySelector('input[name="start_date"]');
                                const existingEndDate = mainForm.querySelector('input[name="end_date"]');

                                if (existingStartDate) existingStartDate.remove();
                                if (existingEndDate) existingEndDate.remove();

                                // Create and append new hidden inputs with the current date values
                                if (startDateValue) {
                                    const startDateInput = document.createElement('input');
                                    startDateInput.type = 'hidden';
                                    startDateInput.name = 'start_date';
                                    startDateInput.value = startDateValue;
                                    mainForm.appendChild(startDateInput);
                                }

                                if (endDateValue) {
                                    const endDateInput = document.createElement('input');
                                    endDateInput.type = 'hidden';
                                    endDateInput.name = 'end_date';
                                    endDateInput.value = endDateValue;
                                    mainForm.appendChild(endDateInput);
                                }

                                // Dispatch a custom event to notify other components about the date range change
                                document.dispatchEvent(new CustomEvent('dateRangeChanged', {
                                    detail: {
                                        startDate: startDateValue,
                                        endDate: endDateValue,
                                        division: divisionFilter.value,
                                        site: siteFilter.value
                                    }
                                }));

                                // Show loading state
                                contentWrapper.style.display = 'none';
                                skeletonLoader.style.display = 'block';

                                mainFilterForm.submit();
                            });
                        });
                    </script>
                    <div class="row mt-0 mb-3">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm h-150" style="border-radius: 10px;">
                                <div class="p-2" style="border-radius: 5px;">
                                    <h5 class="card-title mb-0 font-weight-bold text-uppercase">
                                        <i class="mdi mdi-chart-line me-2"></i>
                                        Grafik Pendapatan <?php echo e($dateRangeText ?? "Bulanan $currentYear"); ?>

                                        <?php if(isset($divisionFilter) && $divisionFilter): ?>
                                            - Divisi: <?php echo e($divisionFilter); ?>

                                        <?php endif; ?>
                                        <?php if(isset($siteFilter) && $siteFilter): ?>
                                            - Site: <?php echo e(\App\Models\Site::where('site_id', $siteFilter)->first()->site_name ?? $siteFilter); ?>

                                        <?php endif; ?>
                                    </h5>
                                </div>
                                <div class="card-body pl-3 pr-3 pt-0 pb-0">
                                    <div style="height: 300px; position: relative;">
                                        <canvas id="monthlyInvoiceChart"></canvas>
                                        <div id="monthlyInvoiceData"
                                            data-monthly-invoice="<?php echo e(json_encode($monthlyInvoiceData)); ?>"
                                            data-current-year="<?php echo e($currentYear); ?>"
                                            style="display: none;">
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row g-3">
                        <?php $__currentLoopData = $sitesData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($site['site_id'] == 'DH') { continue; } ?>
                        <div class="col-xl-4 col-md-6 col-sm-12">
                            <div class="card rounded-3" data-site-id="<?php echo e($site['site_id']); ?>">
                                <div class="card-header">
                                    <h4 class="card-title">
                                        Pencapaian Target <?php echo e($site['site_name']); ?><?php echo e(isset($site['site_address']) && $site['site_address'] ? ' - ' . $site['site_address'] : ''); ?>

                                        <?php if(isset($divisionFilter) && $divisionFilter): ?>
                                            <small class="d-block text-muted">Divisi: <?php echo e($divisionFilter); ?></small>
                                        <?php endif; ?>
                                    </h4>
                                </div>
                                <div class="card-body p-4">
                                    <!-- PO vs Invoice Achievement Section -->
                                    <div class="mb-3" id="target-achievement-<?php echo e($site['site_id']); ?>">
                                        <div class="progress achievement-progress">
                                            <?php
                                            $invoicePercentage = min($site['invoice_percentage'] ?? 0, 100);
                                            ?>
                                            <div class="progress-bar <?php echo e($invoicePercentage >= 20 ? 'bg-success text-white' : 'text-secondary'); ?>"
                                                style="width: <?php echo e($invoicePercentage); ?>%">
                                                <?php echo e(number_format($invoicePercentage, 1)); ?>%
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between mt-2 fs-6">
                                            <span>Target PO : <br> <strong class="h5 font-weight-bold text-blue-800">Rp <?php echo e(number_format($site['total_pos_amount'], 0, ',', '.')); ?></strong></span>
                                            <span>Invoice : <br> <strong class="h5 font-weight-bold text-blue-800">Rp <?php echo e(number_format($site['ready_invoices_amount'], 0, ',', '.')); ?></strong></span>
                                        </div>
                                        <div class="d-flex justify-content-between mt-1 text-muted small">
                                            <span><?php echo e($site['total_pos_count']); ?> Transaksi</span>
                                            <span><?php echo e($site['ready_invoices_count']); ?> Invoice</span>
                                        </div>
                                    </div>
                                    <hr>

                                    <!--Monthly ReportCard -->
                                    <?php if(isset($site['jasa_karyawan'])): ?>
                                    <div class="jasa-karyawan-section mt-3">
                                        <h5 class="mb-3 font-weight-bold">Monthly Report</h5>
                                        <div class="card bg-light mb-3">
                                            <div class="card-body p-3">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="mb-1">Total Jasa</h6>
                                                        <h5 class="mb-0 text-primary">Rp <?php echo e(number_format($site['jasa_karyawan']['total_amount'] ?? 0, 0, ',', '.')); ?></h5>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">Status Selesai</h6>
                                                        <h5 class="mb-0 text-success">Rp <?php echo e(number_format($site['jasa_karyawan']['done_amount'] ?? 0, 0, ',', '.')); ?></h5>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between mt-3">
                                                    <div class="text-center">
                                                        <span class="d-block status-badge status-submitted"><?php echo e($site['jasa_karyawan']['submitted_count'] ?? 0); ?></span>
                                                        <small>Diajukan</small>
                                                    </div>
                                                    <div class="text-center">
                                                        <span class="d-block status-badge status-approved"><?php echo e($site['jasa_karyawan']['approved_count'] ?? 0); ?></span>
                                                        <small>Disetujui</small>
                                                    </div>
                                                    <div class="text-center">
                                                        <span class="d-block status-badge status-rejected"><?php echo e($site['jasa_karyawan']['rejected_count'] ?? 0); ?></span>
                                                        <small>Ditolak</small>
                                                    </div>
                                                    <div class="text-center">
                                                        <span class="d-block status-badge status-done"><?php echo e($site['jasa_karyawan']['done_count'] ?? 0); ?></span>
                                                        <small>Selesai</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Status Indicators -->
                                    <div class="status-indicators">
                                        <div class="status-card" style="background-color: rgb(252, 242, 223);" title="Part Out namun PO belum dikeluarkan">
                                            <div class="d-flex justify-content-between">
                                                <h5 class="m-0 font-weight-bold text-dark">Belum PO</h5>
                                                <h6 class="mb-0 text-dark"><?php echo e($site['belum_po'] ?? 0); ?> Part</h6>
                                            </div>
                                            <div class="h5 text-left text-dark">Rp <?php echo e(number_format($site['belum_po_amount'] ?? 0, 0, ',', '.')); ?></div>
                                        </div>
                                        <div class="status-card" style="background-color: rgb(224, 234, 235);" title="PO sudah keluar, Namun Invoice belum ada">
                                            <div class="d-flex justify-content-between">
                                                <h5 class="m-0 font-weight-bold text-dark">Proses Invoice</h5>
                                                <h6 class="mb-0 text-dark"><?php echo e($site['proses_po'] ?? 0); ?> Part</h6>
                                            </div>
                                            <div class="h5 text-left text-dark">Rp <?php echo e(number_format($site['proses_po_amount'] ?? 0, 0, ',', '.')); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="unitsModal" tabindex="-1" aria-labelledby="unitsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-fullscreen">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="unitsModalLabel">Daftar Unit</h5>
                            <div class="d-flex">
                                <button type="button" class="btn btn-sm btn-light ms-2" data-bs-dismiss="modal">
                                    <i class="mdi mdi-close"></i> Tutup
                                </button>
                            </div>
                        </div>
                        <div class="modal-body p-0">
                            <div class="container-fluid py-4">
                                <div class="text-center mb-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">...</span>
                                    </div>
                                    <p class="mt-2">Memuat data...</p>
                                </div>
                                <div id="unitsModalContent" class="units-modal-container" style="display: none;">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead class="bg-light">
                                                <tr>
                                                    <th>No</th>
                                                    <th>Kode Unit</th>
                                                    <th>Nama Unit</th>
                                                    <th>Total Harga</th>
                                                    <th>Status</th>
                                                    <th>Catatan</th>
                                                    <th>Tanggal</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody id="unitsTableBody">
                                                <!-- Table content will be loaded here via AJAX -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Parts Detail Modal -->
            <div class="modal fade" id="partsDetailModal" tabindex="-1" aria-labelledby="partsDetailModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl" style="max-width: 90%;">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title" id="partsDetailModalLabel">Detail Parts Unit </h5>
                            <div class="d-flex">
                                <button type="button" class="btn btn-sm btn-light ms-2" data-bs-dismiss="modal">
                                    <i class="mdi mdi-close"></i> Tutup
                                </button>
                            </div>
                        </div>
                        <div class="modal-body">
                            <div class="unit-info mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Kode Unit:</strong> <span id="detail-unit-code"></span></p>
                                        <p><strong>Nama Unit:</strong> <span id="detail-unit-name"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Status:</strong> <span id="detail-status"></span></p>
                                        <p><strong>Catatan:</strong> <span id="detail-notes"></span></p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <!-- Parts Table Column -->
                                <div class="col-md-12">
                                    <h5 class="mb-3">Daftar Part</h5>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead class="bg-light">
                                                <tr>
                                                    <th>No</th>
                                                    <th>Kode Part</th>
                                                    <th>Nama Part</th>
                                                    <th>Jumlah</th>
                                                    <th>Harga</th>
                                                    <th>Total</th>
                                                </tr>
                                            </thead>
                                            <tbody id="partsDetailTableBody">
                                                <!-- Parts detail will be loaded here -->
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th colspan="5" class="text-end">Total:</th>
                                                    <th id="parts-total-price">Rp 0</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Part Type Detail Modal -->
            <div class="modal fade" id="partTypeDetailModal" tabindex="-1" aria-labelledby="partTypeDetailModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg" style="max-width: 80%;">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="partTypeDetailModalLabel">Detail Part</h5>
                            <div class="d-flex">
                                <button type="button" class="btn btn-sm btn-light ms-2" data-bs-dismiss="modal">
                                    <i class="mdi mdi-close"></i> Tutup
                                </button>
                            </div>
                        </div>
                        <div class="modal-body">
                            <div class="part-type-header mb-3">
                                <div class="d-flex align-items-center">
                                    <div id="part-type-icon" class="fas fa-user-tie bg-primary text-white me-3" style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                                        <i class="mdi mdi-air-conditioner" style="font-size: 24px;"></i>
                                    </div>
                                    <div>
                                        <h4 id="part-type-name" class="mb-0">Part Type</h4>
                                        <p class="text-muted mb-0">Total Pendapatan: <span id="part-type-revenue" class="fw-bold">Rp 0</span></p>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="bg-light">
                                        <tr>
                                            <th>No</th>
                                            <th>Kode Part</th>
                                            <th>Nama Part</th>
                                            <th>Jumlah</th>
                                            <th>Total</th>
                                            <th>Kontribusi</th>
                                        </tr>
                                    </thead>
                                    <tbody id="partTypeDetailTableBody">
                                        <!-- Part type details will be loaded here -->
                                    </tbody>
                                </table>
                            </div>

                            <div id="part-type-empty-state" class="text-center py-4" style="display: none;">
                                <div class="empty-state-icon mb-3">
                                    <i id="part-type-empty-icon" class="mdi mdi-air-conditioner text-a11y-secondary" style="font-size: 48px;"></i>
                                </div>
                                <h5 class="empty-state-title">Belum Ada Data Penjualan</h5>
                                <p class="empty-state-text">Belum ada data penjualan untuk tipe part ini pada periode yang dipilih.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--Monthly ReportDetail Modal -->
            <div class="modal fade" id="jasaKaryawanDetailModal" tabindex="-1" aria-labelledby="jasaKaryawanDetailModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl" style="max-width: 90%;">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title" id="jasaKaryawanDetailModalLabel">Detail Monthly Report</h5>
                            <div class="d-flex">
                                <button type="button" class="btn btn-sm btn-light ms-2" data-bs-dismiss="modal">
                                    <i class="mdi mdi-close"></i> Tutup
                                </button>
                            </div>
                        </div>
                        <div class="modal-body">
                            <div class="jasa-karyawan-header mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="fas fa-user-tie bg-info text-white me-3" style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                                        <i class="mdi mdi-account-hard-hat" style="font-size: 24px;"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0">Monthly Report</h4>
                                        <p class="text-muted mb-0">Total Pendapatan: <span class="fw-bold">Rp <?php echo e(number_format($jasaKaryawanData['total_amount_with_ppn'], 0, ',', '.')); ?></span></p>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="bg-light">
                                        <tr>
                                            <th>No</th>
                                            <th>Site</th>
                                            <th>Karyawan</th>
                                            <th>Tanggal</th>
                                            <th>Jumlah</th>
                                            <th>Status</th>
                                            <th>Catatan</th>
                                            <th>File</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $jasaKaryawanData['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($index + 1); ?></td>
                                            <td><?php echo e($item->site->site_name ?? '-'); ?></td>
                                            <td><?php echo e($item->employee->name ?? '-'); ?></td>
                                            <td><?php echo e(\Carbon\Carbon::parse($item->date)->format('d-m-Y')); ?></td>
                                            <td>Rp <?php echo e(number_format($item->amount, 0, ',', '.')); ?></td>
                                            <td>
                                                <?php if($item->status == 'submitted'): ?>
                                                <span class="badge bg-secondary">Diajukan</span>
                                                <?php elseif($item->status == 'approved'): ?>
                                                <span class="badge bg-primary">Disetujui</span>
                                                <?php elseif($item->status == 'rejected'): ?>
                                                <span class="badge bg-danger">Ditolak</span>
                                                <?php elseif($item->status == 'done'): ?>
                                                <span class="badge bg-success">Selesai</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($item->notes ?? '-'); ?></td>
                                            <td>
                                                <?php if($item->file_path): ?>
                                                <a href="<?php echo e(asset('assets/jasa_karyawan/' . $item->file_path)); ?>" target="_blank" class="btn btn-sm btn-info">
                                                    <i class="mdi mdi-file-document"></i> Lihat
                                                </a>
                                                <?php else: ?>
                                                -
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="sitesData" data-sites-data="" style="display: none;"></div>
        <div id="bestPartsSettings"
            data-limit="<?php echo e(session('best_parts_limit', 5)); ?>"
            data-sort-by="<?php echo e(session('best_parts_sort_by', 'value')); ?>"
            style="display: none;">
        </div>
    </div>
    <script src="<?php echo e(asset('assets/js/vendor.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/app.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/superadmin-dashboard-site-details.js')); ?>"></script>
    <script src="<?php echo e(asset('js/superadmin-dashboard-charts.js')); ?>"></script>
    <script src="<?php echo e(asset('js/superadmin-dashboard-best-parts.js')); ?>"></script>
    <?php echo $__env->yieldContent('resourcesite'); ?>
</body>
</html><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/superadmin/dashboard.blade.php ENDPATH**/ ?>