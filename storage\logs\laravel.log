[2025-05-27 16:36:21] local.INFO: User kasir logged in successfully. employee_id: KASIR001, site_id: DH  
[2025-05-28 09:22:39] local.INFO: User kasir logged in successfully. employee_id: KASIR001, site_id: DH  
[2025-05-28 11:28:12] local.INFO: User pwb20212025 logged in successfully using token. employee_id: 666, site_id:   
[2025-05-28 11:28:15] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 11:28:15] local.INFO: Sites Data Request {"start_date":"2025-05-01","end_date":"2025-05-31","month":null,"division":null,"site":null} 
[2025-05-28 11:28:15] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 11:28:15] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 11:28:15] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 11:28:15] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-05-01 00:00:00 and 2025-05-31 23:59:59 and `status` = belum po))  
[2025-05-28 11:28:15] local.INFO: Sites Data Response {"count":4} 
[2025-05-28 11:28:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 11:28:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
[2025-05-28 12:11:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-05-31 23:59:59"} 
